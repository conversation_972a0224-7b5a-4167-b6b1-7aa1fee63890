
<?php defined('BASEPATH') or exit('No direct script access allowed');

class Wacs extends MY_Controller {

    public function __construct()
    {

        parent::__construct();
        auth_check(); // check login auth
        $this->rbac->check_module_access();
        $baseUrl = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        define('BASE_URL', $baseUrl);
    }
    
    public function authorizeWacsUser()
    {
        try {
            $baseUrl = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
            $username = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_username : $this->settings->wacs_live_username;
            $password = $this->settings->wacs_is_live == 0 ? $this->settings->wacs_test_password : $this->settings->wacs_live_password;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/login');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(array(
                'username' => $username,
                'password' => $password
            )));
            
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                $error = 'Network Error: ' . curl_error($ch);
                log_message('error', $error);
                curl_close($ch);
                return null;
            }
            curl_close($ch);
            $response = json_decode($response);
            if (isset($response->data->token)) {
                return $response->data->token;
            } else {
                $error = 'WACS Auth Error: ' . json_encode($response);
                log_message('error', $error);
                return null;
            }
        } catch (Exception $e) {
            log_message('error', 'Exception in authorizeWacsUser: ' . $e->getMessage());
            return null;
        }
    }

    public function checkEligibilityForm() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/check_eligibility');
        $this->load->view('admin/includes/_footer');
    }

    
    public function checkEligibility($ippis_number = null) {
        $this->rbac->check_operation_access();
        if (!$ippis_number) {
            echo json_encode(['success' => false, 'message' => 'IPPIS number is required.']);
            return;
        }
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/customer/' . urlencode($ippis_number) . '/loan-eligibility');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        $json = json_decode($response, true);
        $eligible = null;
        if (isset($json['success']) && $json['success'] && isset($json['data']['eligibility'])) {
            $eligible = $json['data']['eligibility'];
            $this->load->model('admin/Onboarded_wacs_customers_model');
            $existing = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis_number);
            if ($existing) {
                $this->Onboarded_wacs_customers_model->update_eligibility_by_ippis($ippis_number, $eligible);
            } else {
                $this->Onboarded_wacs_customers_model->insert_onboarded_customer([
                    'ippis_number' => $ippis_number,
                    'current_eligibility' => $eligible
                ]);
            }
        }
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
        } else {
            echo $response;
        }
        curl_close($ch);
    }

	/**
     * AJAX endpoint to get onboarded customer by IPPIS number
     */
    public function ajaxGetCustomerByIppis() {
        // Accept both GET and POST parameters for flexibility
        $ippis = $this->input->post('ippis_number', true) ?: $this->input->get('ippis_number', true);
        if (!$ippis) {
            echo json_encode(['success' => false, 'message' => 'IPPIS number required.']);
            return;
        }
        $this->load->model('admin/Onboarded_wacs_customers_model');
        $customer = $this->Onboarded_wacs_customers_model->get_by_ippis($ippis);
        if ($customer) {
            echo json_encode(['success' => true, 'customer' => $customer]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Customer not found.']);
        }
    }

    public function index()
    {
        $this->rbac->check_operation_access();
        // Get stats from onboarded_wacs_customers and wacs_loans tables
        $this->load->database();
        // Customers count
        $stats = [
            'customers' => 0,
            'loans' => 0,
            'total_disbursed' => 0,
            'total_repaid' => 0
        ];
        $stats['customers'] = $this->db->count_all('onboarded_wacs_customers');
        $stats['loans'] = $this->db->count_all('wacs_loans');
        $stats['total_disbursed'] = (float) $this->db->select_sum('disbursed_amount')->get('wacs_loans')->row('disbursed_amount');
        $stats['total_repaid'] = (float) $this->db->select_sum('repayment_amount')->get('wacs_loans')->row('repayment_amount');

        // Get recent loans (last 10)
        $recent_loans = $this->db->order_by('start_date', 'DESC')->limit(10)->get('wacs_loans')->result_array();

        $data = [
            'stats' => $stats,
            'recent_loans' => $recent_loans
        ];
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/dashboard', $data);
        $this->load->view('admin/includes/_footer');
    }


    public function update(){

        if (isset($_POST['updatetype'])) {
                $body = NULL;
                if ($_POST['updatetype'] == 'batch') {
                    
                    $body_arr = NULL;
                    $config['upload_path'] = './uploads/';
                    $config['allowed_types'] = 'csv';
                    $config['max_size'] = 5120; // 5MB
                    $this->load->library('upload', $config);

                    if (!$this->upload->do_upload('csv_file')) {
                        $error = $this->upload->display_errors();
                        echo json_encode(['status'=>false,'message'=>$error]);
                    } else {
                        $csvData = file_get_contents($_FILES['csv_file']['tmp_name']);
                        $rows = explode("\n", $csvData);
                        $totalRecords = 0;
                        foreach ($rows as $row) {
                            $values = str_getcsv($row);
                            if (!empty($values)) {
                                
                            
                                if (count($values) >= 2) {
                                    $totalRecords++;
                                    $body_arr[] =  [
                                        "accountNumber" => $values[0],
                                        "bvn" => $values[1],
                                        "firstName" => $values[2],
                                        "lastName" => $values[3],
                                    ];
                                }
                                
                            }
                        }

                    }
                    $body = $body_arr;
                }elseif($_POST['updatetype'] == 'single'){
                    
                    $body = array(
                        array(
                            "accountNumber" => $this->input->post('nuban'),
                            "firstName" => $this->input->post('firstName'),
                            "lastName" => $this->input->post('lastName'),
                            "bvn" => $this->input->post('bvn')
                        )
                    );
                                
                }
            $headers = array( 
                'x-tag: ****************************************************************************************************************',
                'Content-Type: application/json'
            );
            $json_body = json_encode($body);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://api.veendhq.com/client/consumermicrofinancebak/update-bvn');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $response = curl_exec($ch);

            if (curl_errno($ch)) {
                $error = 'Error: ' . curl_error($ch);
                log_message('error', $error);
                echo $error;
            } else {
                echo $response;
            }

            curl_close($ch);
        }else{
            echo json_encode(['status'=>false,'msg'=>'i dont understand your request']);
        }
    }

    public function createLoanProduct() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect($this->agent->referrer());
            return;
        }

        $body = $this->input->post();
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/create');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            $this->session->set_flashdata('error', 'Network error occurred while creating loan product.');
            redirect($this->agent->referrer());
            return;
        }

        $result = json_decode($response, true);
        
        if (isset($result['success']) && $result['success']) {
            $msg = isset($result['message']) ? $result['message'] : 'Loan product created successfully.';
            $this->session->set_flashdata('success', $msg);
            redirect($this->agent->referrer());
            return;
        }

        // Handle different types of error responses
        $errorMsg = '';
        if (isset($result['message'])) {
            $errorMsg = $result['message'];
        }

        if (isset($result['errors']) && is_array($result['errors'])) {
            $formattedErrors = [];
            foreach ($result['errors'] as $field => $error) {
                if (is_array($error)) {
                    $formattedErrors[] = is_numeric($field) ? implode('<br>', $error) : $field . ': ' . implode(', ', $error);
                } else {
                    $formattedErrors[] = is_numeric($field) ? $error : $field . ': ' . $error;
                }
            }
            if (!empty($formattedErrors)) {
                $errorMsg .= (!empty($errorMsg) ? '<br>' : '') . implode('<br>', $formattedErrors);
            }
        }

        if (empty($errorMsg)) {
            $errorMsg = 'Error creating loan product. Please check your input and try again.';
        }

        $this->session->set_flashdata('error', $errorMsg);
        redirect($this->agent->referrer());
    }

    public function createLoanProductForm() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/create_loan_product');
        $this->load->view('admin/includes/_footer');
    }

    public function updateLoanProduct($product_id) {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect($this->agent->referrer());
            return;
        }
        $body = $this->input->post();
        // Convert feature to array if comma separated string
        if (isset($body['feature']) && !is_array($body['feature'])) {
            $body['feature'] = array_map('trim', explode(',', $body['feature']));
        }
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/' . $product_id . '/update');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            $this->session->set_flashdata('error', 'Network error occurred while updating loan product.');
            redirect($this->agent->referrer());
            return;
        }

        $result = json_decode($response, true);
        if (isset($result['success']) && $result['success']) {
            $msg = isset($result['message']) ? $result['message'] : 'Loan product updated successfully.';
            $this->session->set_flashdata('success', $msg);
            redirect($this->agent->referrer());
            return;
        }
        // Handle different types of error responses
        $errorMsg = '';
        if (isset($result['message'])) {
            $errorMsg = $result['message'];
        }
        if (isset($result['errors']) && is_array($result['errors'])) {
            $formattedErrors = [];
            foreach ($result['errors'] as $field => $error) {
                if (is_array($error)) {
                    $formattedErrors[] = is_numeric($field) ? implode('<br>', $error) : $field . ': ' . implode(', ', $error);
                } else {
                    $formattedErrors[] = is_numeric($field) ? $error : $field . ': ' . $error;
                }
            }
            if (!empty($formattedErrors)) {
                $errorMsg .= (!empty($errorMsg) ? '<br>' : '') . implode('<br>', $formattedErrors);
            }
        }
        if (empty($errorMsg)) {
            $errorMsg = 'Error updating loan product. Please check your input and try again.';
        }
        $this->session->set_flashdata('error', $errorMsg);
        redirect($this->agent->referrer());
    }

    public function listLoanProducts() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $data['products'] = [];
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
        } else {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/index');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Authorization: Bearer ' . $token,
                'Accept: application/json',
                'Content-Type: application/json'
            ));
            $response = curl_exec($ch);
            if (curl_errno($ch)) {
                $error = 'Network Error: ' . curl_error($ch);
                log_message('error', $error);
                $data['products'] = [];
                $this->session->set_flashdata('error', $error);
            } else {
                $result = json_decode($response, true);
                $data['products'] = isset($result['data']) ? $result['data'] : [];
            }
            curl_close($ch);
        }
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/list_loan_products', $data);
        $this->load->view('admin/includes/_footer');
    }

    public function editLoanProduct($product_id) {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect($this->agent->referrer());
            return;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/' . $product_id);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        curl_close($ch);
        $result = json_decode($response, true);
        $product = isset($result['data']) ? $result['data'] : [];
        if (empty($product)) {
            $errorMsg = isset($result['message']) ? $result['message'] : 'Loan product not found.';
            $this->session->set_flashdata('error', $errorMsg);
            redirect($this->agent->referrer());
            return;
        }
        $data['product'] = $product;
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/edit_loan_product', $data);
        $this->load->view('admin/includes/_footer');
    }

    /**
     * Loads the edit loan product form with product details from POST
     */
    public function editLoanProductForm() {
        $this->rbac->check_operation_access();
        $product = $this->input->post('product');
        if (empty($product) || !is_array($product)) {
            $this->session->set_flashdata('error', 'Loan product data not found or invalid.');
            redirect($this->agent->referrer());
            return;
        }
        $data['product'] = $product;
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/edit_loan_product', $data);
        $this->load->view('admin/includes/_footer');
    }

    public function activateLoanProduct($product_id) {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect('admin/wacs/listLoanProducts');
            return;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/' . $product_id . '/activate');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            $this->session->set_flashdata('error', $error);
        } else {
            $result = json_decode($response, true);
            if (isset($result['success']) && $result['success']) {
                $this->session->set_flashdata('success', $result['message'] ?? 'Loan Product Activated Successfully');
            } else {
                $this->session->set_flashdata('error', $result['message'] ?? 'Activation failed.');
            }
        }
        curl_close($ch);
        redirect('admin/wacs/listLoanProducts');
    }

    public function deactivateLoanProduct($product_id) {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
            redirect('admin/wacs/listLoanProducts');
            return;
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/' . $product_id . '/deactivate');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            $this->session->set_flashdata('error', $error);
        } else {
            $result = json_decode($response, true);
            if (isset($result['success']) && $result['success']) {
                $this->session->set_flashdata('success', $result['message'] ?? 'Loan Product Deactivated Successfully');
            } else {
                $this->session->set_flashdata('error', $result['message'] ?? 'Deactivation failed.');
            }
        }
        curl_close($ch);
        redirect('admin/wacs/listLoanProducts');
    }

       public function allLoans() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        $loans = [];
        if ($token) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loans');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $token,
                    'Accept: application/json',
                    'Content-Type: application/json'
            ));
            $response = curl_exec($ch);
            if (!curl_errno($ch)) {
                $result = json_decode($response, true);
                if (isset($result['data'])) {
                    $loans = $result['data'];
                }
            } else {
                $error = 'Network Error: ' . curl_error($ch);
                log_message('error', $error);
                $this->session->set_flashdata('error', $error);
            }
            curl_close($ch);
        } else {
            $this->session->set_flashdata('error', 'Authorization failed. No token received.');
        }
        $data = ['loans' => $loans];
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/all_loans', $data);
        $this->load->view('admin/includes/_footer');
    }

      /**
     * AJAX endpoint for paginated, filterable, multi-search WACS loans list
     */
    public function ajaxListLoans() {
        $q = $this->input->get('q', true);
        $date_from = $this->input->get('date_from', true);
        $date_to = $this->input->get('date_to', true);
        $status = $this->input->get('status', true);
        $page = max(1, (int)$this->input->get('page', true));
        $per_page = 20;
        $offset = ($page-1)*$per_page;
        $this->db->from('wacs_loans');
        if ($q) {
            $this->db->group_start()
                ->like('loan_id', $q)
                ->or_like('customer_ippis', $q)
                ->or_like('employee_name', $q)
                ->or_like('debtor', $q)
                ->or_like('phone', $q)
                ->or_like('account_number', $q)
            ->group_end();
        }
        if ($status) $this->db->where('(status_loan = "' . $this->db->escape_str($status) . '" OR status = "' . $this->db->escape_str($status) . '")');
        if ($date_from) $this->db->where('DATE(created_at) >=', $date_from);
        if ($date_to) $this->db->where('DATE(created_at) <=', $date_to);
        $total = $this->db->count_all_results('', false);
        $this->db->order_by('start_date','desc');
        $this->db->limit($per_page, $offset);
        $loans = $this->db->get()->result_array();
        // Pagination
        $this->load->library('pagination');
        $config['base_url'] = base_url('admin/wacs/ajaxListLoans');
        $config['total_rows'] = $total;
        $config['per_page'] = $per_page;
        $config['use_page_numbers'] = true;
        $config['page_query_string'] = true;
        $config['query_string_segment'] = 'page';
        $config['full_tag_open'] = '<ul class="pagination">';
        $config['full_tag_close'] = '</ul>';
        $config['attributes'] = ['class' => 'page-link'];
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $this->pagination->initialize($config);
        $pagination = $this->pagination->create_links();
        $html = $this->load->view('admin/wacs/_all_loans_table', [
            'loans' => $loans,
            'pagination' => $pagination,
            'total' => $total
        ], true);
        echo $html;
    }

    /**
     * Onboard a new customer to WACS
     * Endpoint: POST /admin/wacs/onboardCustomer
     */
    public function onboardCustomer() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }
        //var_dump($baseUrl);die();
        // $input = json_decode(file_get_contents('php://input'), true);
        // if (!$input) {
            $input = $this->input->post();
        //}
        
        $body = [
            'ippis_number' => $input['ippis_number'] ?? '',
            'bvn' => $input['bvn'] ?? '',
            'account_number' => $input['account_number'] ?? '',
            'first_name' => $input['first_name'] ?? '',
            'last_name' => $input['last_name'] ?? '',
            'phone_number' => $input['phone_number'] ?? '',
            'email' => $input['email'] ?? '',
            'bank' => $input['bank'] ?? '',
            'next_of_kin' => [
                'name' => $input['next_of_kin']['name'] ?? $input['next_of_kin_name'] ?? '',
                'phone' => $input['next_of_kin']['phone'] ?? $input['next_of_kin_phone'] ?? '',
                'address' => $input['next_of_kin']['address'] ?? $input['next_of_kin_address'] ?? ''
            ],
            'referee' => [
                [
                    'name' => $input['referee'][0]['name'] ?? $input['referee_name'] ?? '',
                    'phone' => $input['referee'][0]['phone'] ?? $input['referee_phone'] ?? '',
                    'address' => $input['referee'][0]['address'] ?? $input['referee_address'] ?? ''
                ]
            ]
        ];
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/customers/register');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
        } else {
            $result = json_decode($response, true);
            // If onboarding is successful, save to local table
            if (isset($result['success']) && $result['success'] && isset($result['data'])) {
                $this->insertOnboardedWacsCustomer($result['data']);
                echo $response;
            } else {
                // Show all errors, not just summary
                $errorMsg = isset($result['message']) ? $result['message'] : 'Error onboarding customer.';
                $errors = [];
                if (isset($result['errors']) && is_array($result['errors'])) {
                    $formattedErrors = [];
                    foreach ($result['errors'] as $field => $error) {
                        if (is_array($error)) {
                            $formattedErrors[] = is_numeric($field) ? implode('<br>', $error) : $field . ': ' . implode(', ', $error);
                            $errors[$field] = $error;
                        } else {
                            $formattedErrors[] = is_numeric($field) ? $error : $field . ': ' . $error;
                            $errors[$field] = [$error];
                        }
                    }
                    if (!empty($formattedErrors)) {
                        $errorMsg .= (!empty($errorMsg) ? '<br>' : '') . implode('<br>', $formattedErrors);
                    }
                }
                echo json_encode(['success' => false, 'message' => $errorMsg, 'errors' => $errors]);
            }
        }
        curl_close($ch);
    }

    /**
     * Show the standalone WACS customer creation form
     */
    public function createWacsCustomerForm() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/create_wacs_customer');
        $this->load->view('admin/includes/_footer');
    }

    /**
     * Process standalone WACS customer onboarding (AJAX)
     * Endpoint: POST /admin/wacs/createWacsCustomer
     */
    public function createWacsCustomer() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }
        $input = $this->input->post();
        $body = [
            'ippis_number' => $input['ippis_number'] ?? '',
            'bvn' => $input['bvn'] ?? '',
            'account_number' => $input['account_number'] ?? '',
            'first_name' => $input['first_name'] ?? '',
            'last_name' => $input['last_name'] ?? '',
            'phone_number' => $input['phone_number'] ?? '',
            'email' => $input['email'] ?? '',
            'bank' => $input['bank'] ?? '',
            'next_of_kin' => [
                'name' => $input['next_of_kin_name'] ?? '',
                'phone' => $input['next_of_kin_phone'] ?? '',
                'address' => $input['next_of_kin_address'] ?? ''
            ],
            'referee' => [
                [
                    'name' => $input['referee_name'] ?? '',
                    'phone' => $input['referee_phone'] ?? '',
                    'address' => $input['referee_address'] ?? ''
                ]
            ]
        ];
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/customers/register');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
        } else {
            $result = json_decode($response, true);
            if (isset($result['success']) && $result['success'] && isset($result['data'])) {
                $this->insertOnboardedWacsCustomer($result['data']);
                echo $response;
            } else {
                $errorMsg = isset($result['message']) ? $result['message'] : 'Error onboarding customer.';
                $errors = [];
                if (isset($result['errors']) && is_array($result['errors'])) {
                    $formattedErrors = [];
                    foreach ($result['errors'] as $field => $error) {
                        if (is_array($error)) {
                            $formattedErrors[] = is_numeric($field) ? implode('<br>', $error) : $field . ': ' . implode(', ', $error);
                            $errors[$field] = $error;
                        } else {
                            $formattedErrors[] = is_numeric($field) ? $error : $field . ': ' . $error;
                            $errors[$field] = [$error];
                        }
                    }
                    if (!empty($formattedErrors)) {
                        $errorMsg .= (!empty($errorMsg) ? '<br>' : '') . implode('<br>', $formattedErrors);
                    }
                }
                echo json_encode(['success' => false, 'message' => $errorMsg, 'errors' => $errors]);
            }
        }
        curl_close($ch);
    }

    /**
     * Apply for a loan for a customer
     * Endpoint: POST /admin/wacs/applyLoan
     */
    public function applyLoan() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $this->input->post();
        }
        $body = [
            'customer_id' => $input['customer_id'] ?? '',
            'loan_product_id' => $input['loan_product_id'] ?? '',
            'amount' => $input['amount'] ?? ''
        ];
        $json_body = json_encode($body);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/customers/loan-application');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
        } else {
            echo $response;
        }
        curl_close($ch);
    }

    /**
     * AJAX endpoint to get all loan products (for dropdowns)
     * Returns only active products
     */
    public function getLoanProducts() {
        $this->rbac->check_operation_access();
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();
        $products = [];
        if ($token) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl.'/api/v1/lender/loanProduct/index');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Authorization: Bearer ' . $token,
                    'Accept: application/json',
                    'Content-Type: application/json'
            ));
            $response = curl_exec($ch);
            if (!curl_errno($ch)) {
                $result = json_decode($response, true);
                if (isset($result['data'])) {
                    // Only active products
                    $products = array_filter($result['data'], function($p) {
                        return (isset($p['status']) && ($p['status'] === 'active' || $p['status'] === 1)) || (isset($p['is_active']) && $p['is_active'] == 1);
                    });
                }
            }
            curl_close($ch);
        }
        echo json_encode(['success' => true, 'data' => array_values($products)]);
    }

    /**
     * Insert onboarded WACS customer into local table
     * @param array $customerData - associative array of customer fields (from WACS onboarding response)
     * @param string|null $created_by - username or id of admin creating (optional)
     * @return int|bool Insert ID or false
     */
    private function insertOnboardedWacsCustomer($customerData, $created_by = null) {
        $this->load->model('admin/Onboarded_wacs_customers_model');
        $data = [
            'mda' => $customerData['mda'] ?? null,
            'pfa_name' => $customerData['pfa_name'] ?? null,
            'account_name' => $customerData['account_name'] ?? null,
            'account_number' => $customerData['account_number'] ?? null,
            'bank' => $customerData['bank'] ?? null,
            'bank_code' => $customerData['bank_code'] ?? null,
            'bvn' => $customerData['bvn'] ?? null,
            'current_salary' => $customerData['current_salary'] ?? null,
            'current_eligibility' => $customerData['current_eligibility'] ?? null,
            'ippis_number' => $customerData['ippis_number'] ?? null,
            'nationality' => $customerData['nationality'] ?? null,
            'address' => $customerData['address'] ?? null,
            'state' => $customerData['state'] ?? null,
            'employee_status' => $customerData['employee_status'] ?? null,
            'first_name' => $customerData['first_name'] ?? null,
            'last_name' => $customerData['last_name'] ?? null,
            'middle_name' => $customerData['middle_name'] ?? null,
            'phone_number' => $customerData['phone_number'] ?? null,
            'email' => $customerData['email'] ?? null,
            'role' => $customerData['role'] ?? null,
            'created_by' => $created_by ?? ($this->session->userdata('id') ?? null),
        ];
        return $this->Onboarded_wacs_customers_model->insert_onboarded_customer($data);
    }

    public function listOnboardedCustomers() {
        $this->rbac->check_operation_access();
        $this->load->model('admin/Onboarded_wacs_customers_model');
        $customers = $this->Onboarded_wacs_customers_model->db->order_by('id','desc')->get('onboarded_wacs_customers')->result_array();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/list_onboarded_customers', ['customers' => $customers]);
        $this->load->view('admin/includes/_footer');
    }

    public function ajaxListOnboardedCustomers() {
        $this->load->model('admin/Onboarded_wacs_customers_model');
        $q = $this->input->get('q', true);
        $date_from = $this->input->get('date_from', true);
        $date_to = $this->input->get('date_to', true);
        $page = max(1, (int)$this->input->get('page', true));
        $per_page = 20;
        $offset = ($page-1)*$per_page;
        $this->db->from('onboarded_wacs_customers');
        if ($q) {
            $this->db->group_start()
                ->like('ippis_number', $q)
                ->or_like('first_name', $q)
                ->or_like('last_name', $q)
                ->or_like('phone_number', $q)
                ->or_like('account_number', $q)
            ->group_end();
        }
        if ($date_from) $this->db->where('DATE(created_at) >=', $date_from);
        if ($date_to) $this->db->where('DATE(created_at) <=', $date_to);
        $total = $this->db->count_all_results('', false);
        $this->db->order_by('id','desc');
        $this->db->limit($per_page, $offset);
        $customers = $this->db->get()->result_array();
        // Pagination
        $this->load->library('pagination');
        $config['base_url'] = base_url('admin/wacs/ajaxListOnboardedCustomers');
        $config['total_rows'] = $total;
        $config['per_page'] = $per_page;
        $config['use_page_numbers'] = true;
        $config['page_query_string'] = true;
        $config['query_string_segment'] = 'page';
        $config['full_tag_open'] = '<ul class="pagination">';
        $config['full_tag_close'] = '</ul>';
        $config['attributes'] = ['class' => 'page-link'];
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $this->pagination->initialize($config);
        $pagination = $this->pagination->create_links();
        $html = $this->load->view('admin/wacs/_onboarded_customers_table', [
            'customers' => $customers,
            'pagination' => $pagination,
            'total' => $total,
            'offset' => $offset
        ], true);
        echo $html;
    }

    /**
     * Register customer via USSD endpoint
     * Endpoint: POST /admin/wacs/registerCustomerUssd
     */
    public function registerCustomerUssd() {
        $this->rbac->check_operation_access();

        // Get input data
        $ippis_number = $this->input->post('ippis_number', true);
        $bvn = $this->input->post('bvn', true);
        $account_number = $this->input->post('account_number', true);

        // Validate required fields and formats
        $errors = [];

        if (!$ippis_number) {
            $errors['ippis_number'] = 'IPPIS number is required.';
        } elseif (!preg_match('/^[0-9]+$/', $ippis_number)) {
            $errors['ippis_number'] = 'IPPIS number must contain only digits.';
        }

        if (!$bvn) {
            $errors['bvn'] = 'BVN is required.';
        } elseif (!preg_match('/^[0-9]{11}$/', $bvn)) {
            $errors['bvn'] = 'BVN must be exactly 11 digits.';
        }

        if (!$account_number) {
            $errors['account_number'] = 'Account number is required.';
        } elseif (!preg_match('/^[0-9]{10}$/', $account_number)) {
            $errors['account_number'] = 'Account number must be exactly 10 digits.';
        }

        if (!empty($errors)) {
            echo json_encode([
                'success' => false,
                'message' => 'Please correct the following errors:',
                'errors' => $errors
            ]);
            return;
        }

        // Get WACS API configuration
        $baseUrl = ($this->settings->wacs_is_live == 0) ? $this->settings->wacs_test_url : $this->settings->wacs_live_url;
        $token = defined('AUTH_TOKEN') ? AUTH_TOKEN : $this->authorizeWacsUser();

        if (!$token) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed. No token received.']);
            return;
        }

        // Prepare request data
        $requestData = [
            'ippis_number' => $ippis_number,
            'bvn' => $bvn,
            'account_number' => $account_number
        ];

        $json_body = json_encode($requestData);

        // Make API call to WACS
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/v1/lender/customers/register/ussd');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_body);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = 'Network Error: ' . curl_error($ch);
            log_message('error', $error);
            echo json_encode(['success' => false, 'message' => $error]);
            curl_close($ch);
            return;
        }

        curl_close($ch);

        $result = json_decode($response, true);

        if ($httpCode == 200 && isset($result['success']) && $result['success'] && isset($result['data'])) {
            // Save customer data to local database
            $saved = $this->saveOnboardedWacsCustomer($result['data']);

            if ($saved) {
                // Return the successful response
                echo $response;
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Customer registered successfully but failed to save locally. Please contact administrator.'
                ]);
            }
        } else {
            // Handle API errors
            $errorMsg = isset($result['message']) ? $result['message'] : 'Customer registration failed.';

            if (isset($result['errors']) && is_array($result['errors'])) {
                $errorDetails = [];
                foreach ($result['errors'] as $field => $error) {
                    if (is_array($error)) {
                        $errorDetails[] = implode(', ', $error);
                    } else {
                        $errorDetails[] = $error;
                    }
                }
                if (!empty($errorDetails)) {
                    $errorMsg .= ' Details: ' . implode('; ', $errorDetails);
                }
            }

            echo json_encode(['success' => false, 'message' => $errorMsg]);
        }
    }

    /**
     * Save onboarded WACS customer data to local database
     */
    private function saveOnboardedWacsCustomer($data) {
        try {
            $this->load->model('admin/Onboarded_wacs_customers_model');

            $customer = $data['customer'];
            $user = $customer['user'];
            $moreInfo = $data['more_info'];

            // Prepare data for insertion
            $insertData = [
                'customer_id' => $customer['id'],
                'mda' => $customer['mda'],
                'pfa_name' => $customer['pfaName'],
                'account_name' => $customer['accountName'],
                'account_number' => $customer['accountNumber'],
                'bank' => $customer['bank'],
                'bank_code' => $customer['bankCode'],
                'bvn' => $customer['bvn'],
                'gender' => $customer['gender'],
                'current_salary' => $customer['currentSalary'],
                'ippis_number' => $customer['ippisNumber'],
                'nationality' => $customer['nationality'],
                'address' => $customer['address'],
                'state' => $customer['state'],
                'employee_status' => $customer['employeeStatus'],

                // User information
                'user_id' => $user['id'],
                'user_first_name' => $user['firstName'],
                'user_last_name' => $user['lastName'],
                'user_middle_name' => $user['middleName'],
                'user_phone_number' => $user['phoneNumber'],
                'user_email' => $user['email'],
                'user_role' => $user['role'],

                // More info
                'more_info_id' => $moreInfo['id'],
                'data_source' => $moreInfo['data_source'],
                'reference_id' => $moreInfo['reference_id'],
                'more_info_ippis_number' => $moreInfo['ippis_number'],
                'staff_id' => $moreInfo['staff_id'],
                'title' => $moreInfo['title'],
                'first_name' => $moreInfo['first_name'],
                'middle_name' => $moreInfo['middle_name'],
                'surname' => $moreInfo['surname'],
                'full_name' => $moreInfo['full_name'],
                'date_of_birth' => $moreInfo['date_of_birth'],
                'date_of_hire' => $moreInfo['date_of_hire'],
                'marital_status' => $moreInfo['marital_status'],
                'more_info_gender' => $moreInfo['gender'],
                'mobile_number' => $moreInfo['mobile_number'],
                'email_address' => $moreInfo['email_address'],
                'more_info_nationality' => $moreInfo['nationality'],
                'mother_maiden_name' => $moreInfo['mother_maiden_name'],
                'lga_of_origin' => $moreInfo['lga_of_origin'],
                'home_town' => $moreInfo['home_town'],
                'residential_address' => $moreInfo['residential_address'],
                'residential_country' => $moreInfo['residential_country'],
                'residential_state' => $moreInfo['residential_state'],
                'residential_lga' => $moreInfo['residential_lga'],
                'residential_city' => $moreInfo['residential_city'],
                'more_info_mda' => $moreInfo['mda'],
                'department' => $moreInfo['department'],
                'rank' => $moreInfo['rank'],
                'cadre' => $moreInfo['cadre'],
                'grade_name' => $moreInfo['grade_name'],
                'grade_level' => $moreInfo['grade_level'],
                'grade_step' => $moreInfo['grade_step'],
                'date_of_appointment' => $moreInfo['date_of_appointment'],
                'expected_date_of_retirement' => $moreInfo['expected_date_of_retirement'],
                'enrollment_date' => $moreInfo['enrollment_date'],
                'date_of_confirmation' => $moreInfo['date_of_confirmation'],
                'section' => $moreInfo['section'],
                'unit' => $moreInfo['unit'],
                'more_info_pfa_name' => $moreInfo['pfa_name'],
                'pfa_pin' => $moreInfo['pfa_pin'],
                'nhf_number' => $moreInfo['nhf_number'],
                'bank_type' => $moreInfo['bank_type'],
                'bank_name' => $moreInfo['bank_name'],
                'more_info_bank_code' => $moreInfo['bank_code'],
                'bank_branch' => $moreInfo['bank_branch'],
                'more_info_account_number' => $moreInfo['account_number'],
                'account_type' => $moreInfo['account_type'],
                'sort_code' => $moreInfo['sort_code'],
                'payroll_name' => $moreInfo['payroll_name'],
                'trade_union' => $moreInfo['trade_union'],
                'tax_id' => $moreInfo['tax_id'],
                'tax_state' => $moreInfo['tax_state'],
                'payroll_period' => $moreInfo['payroll_period'],
                'employment_status' => $moreInfo['employment_status'],
                'created_at' => $moreInfo['created_at'],
                'updated_at' => $moreInfo['updated_at'],
                'more_info_eligibility' => $moreInfo['eligibility'],
                'eligibility' => $data['eligibility'],
                'response_json' => json_encode($data)
            ];

            // Check if customer already exists
            $existing = $this->Onboarded_wacs_customers_model->get_by_ippis($customer['ippisNumber']);

            if ($existing) {
                // Update existing record
                return $this->Onboarded_wacs_customers_model->update_by_ippis($customer['ippisNumber'], $insertData);
            } else {
                // Insert new record
                return $this->Onboarded_wacs_customers_model->insert_onboarded_customer($insertData);
            }

        } catch (Exception $e) {
            log_message('error', 'Error saving onboarded WACS customer: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Display create loan form view
     */
    public function createLoanForm() {
        $this->rbac->check_operation_access();
        $this->load->view('admin/includes/_header');
        $this->load->view('admin/wacs/create_loan_form');
        $this->load->view('admin/includes/_footer');
    }

    /**
     * Get customer details by ID
     * Endpoint: GET /admin/wacs/getCustomerDetails/{id}
     */
    public function getCustomerDetails($customer_id = null) {
        $this->rbac->check_operation_access();

        if (!$customer_id) {
            echo json_encode(['success' => false, 'message' => 'Customer ID is required.']);
            return;
        }

        $this->load->model('admin/Onboarded_wacs_customers_model');
        $customer = $this->db->get_where('onboarded_wacs_customers', ['id' => $customer_id])->row_array();

        if ($customer) {
            echo json_encode(['success' => true, 'customer' => $customer]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Customer not found.']);
        }
    }

}

