<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Check Customer Loan Eligibility</h1>
                </div>
            </div>
        </div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card">
                        <div class="card-body">
                            <form id="eligibilityForm" method="post" action="#" novalidate>
                                <div class="form-row">
                                    <div class="form-group col-md-4">
                                        <label for="ippis_number">IPPIS Number</label>
                                        <input type="text" class="form-control" id="ippis_number" name="ippis_number" required value="<?php echo isset($_GET['ippis_number']) ? htmlspecialchars($_GET['ippis_number']) : ''; ?>">
                                    </div>
                                    <div class="form-group col-md-4 align-self-end">
                                        <button type="submit" class="btn btn-primary w-100">Check Eligibility</button>
                                    </div>
                                </div>
                            </form>
                            <div id="eligibilityResult" class="mt-4" style="display:none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
$(document).ready(function() {
    // CSRF token setup
    var csrfName = '<?= $this->security->get_csrf_token_name(); ?>';
    var csrfHash = '<?= $this->security->get_csrf_hash(); ?>';

    $('#eligibilityForm').on('submit', function(e) {
        e.preventDefault();
        var ippis = $('#ippis_number').val();
        var resultDiv = $('#eligibilityResult');
        var btn = $(this).find('button[type="submit"]');
        resultDiv.hide().html('');
        if (!ippis) {
            resultDiv.html('<div class="alert alert-danger">Please enter an IPPIS number.</div>').show();
            return;
        }
        btn.prop('disabled', true).text('Checking, please wait...');
        // Only check eligibility, do not check onboarded_wacs_customers here
        $.ajax({
            url: '<?= base_url('admin/wacs/checkEligibility') ?>/' + encodeURIComponent(ippis),
            method: 'GET',
            dataType: 'json',
            success: function(res) {
        btn.prop('disabled', false).text('Check Eligibility');
        if (res && res.success && res.eligibility) {
            var eligibilityAmount = parseFloat(res.eligibility) || 0;
            var eligibilityDisplay = `<h3 class='text-success mb-3'>Eligibility: <span style='font-weight:bold'><span style='font-family:inherit'>&#8358;</span>${eligibilityAmount.toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}</span></h3>`;
            // Now check if customer is onboarded to show loan form or onboarding form
            $.ajax({
                url: '<?= base_url('admin/wacs/ajaxGetCustomerByIppis') ?>',
                method: 'POST',
                data: { ippis_number: ippis, '<?= $this->security->get_csrf_token_name(); ?>': '<?= $this->security->get_csrf_hash(); ?>' },
                dataType: 'json',
                success: function(customerRes) {
                    var html = eligibilityDisplay;
                    if (customerRes && customerRes.success && customerRes.customer) {
                        // Show loan application form
                        var customer_id = customerRes.customer.id;
                        html += `<form id="loanApplicationForm">
                            <input type='hidden' name='customer_id' value='${customer_id}'>
                            <div class='form-group'>
                                <label>IPPIS Number</label>
                                <input type='text' class='form-control' value='${ippis}' readonly>
                            </div>
                            <div class='form-group'>
                                <label for='loan_product_id'>Loan Product</label>
                                <select class='form-control' name='loan_product_id' id='loan_product_id' required></select>
                            </div>
                            <div class='form-group'>
                                <label for='amount'>Amount</label>
                                <div id="amountWarning" class="alert alert-warning" style="display:none;padding:4px 8px;margin-bottom:6px;font-size:90%"></div>
                                <input type='number' class='form-control' name='amount' id='amount' required>
                                <div id="repaymentInfo" class="mt-2" style="display:none;"></div>
                            </div>
                            <button type='submit' class='btn btn-primary'>Apply for Loan</button>
                        </form>`;
                        resultDiv.html(html).show();
                        // Fetch loan products
                        $.get('<?= base_url('admin/wacs/getLoanProducts') ?>', function(productsRes) {
                            var productsMap = {};
                            if (productsRes.success && Array.isArray(productsRes.data)) {
                                productsRes.data.forEach(function(p) {
                                    productsMap[p.id] = p;
                                });
                                var options = productsRes.data
                                    .filter(function(p) { return p.status === 'active' || p.status === 1 || p.is_active === 1; })
                                    .map(function(p) {
                                        var rate = p.interest_rate ? p.interest_rate + '%' : '';
                                        var period = p.payback_period ? p.payback_period : (p.tenure ? p.tenure : '');
                                        var label = `${p.name} - ${rate} - ${period}`;
                                        return `<option value='${p.id}'>${label}</option>`;
                                    });
                                $('#loan_product_id').html(options.join(''));
                            }
                        }, 'json');
                        // Add warning logic for amount field
                        var eligible = eligibilityAmount;
                        $(document).off('input.amountWarning').on('input.amountWarning', '#amount', function() {
                            var val = parseFloat($(this).val());
                            if (eligible && val > eligible) {
                                $('#amountWarning').text('Loan amount is greater than eligible amount').show();
                            } else {
                                $('#amountWarning').hide();
                            }
                        });
                        function updateRepaymentInfo() {
                            var productId = $('#loan_product_id').val();
                            var amount = parseFloat($('#amount').val());
                            var infoDiv = $('#repaymentInfo');
                            if (productsMap[productId] && amount > 0) {
                                var rate = parseFloat(productsMap[productId].interest_rate) || 0;
                                var period = parseInt(productsMap[productId].payback_period || productsMap[productId].tenure) || 1;
                                var totalRepayable = amount + (amount * rate * period / 100);
                                var monthlyRepayment = totalRepayable / period;
                                infoDiv.html(
                                    `<div class='alert alert-info'>Monthly Repayment: <b><span style='font-family:inherit'>&#8358;</span>${monthlyRepayment.toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}</b><br>Tenure: <b>${period} month(s)</b><br>Total Repayable: <b><span style='font-family:inherit'>&#8358;</span>${totalRepayable.toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}</b></div>`
                                ).show();
                            } else {
                                infoDiv.hide();
                            }
                        }
                        $(document).on('input change', '#amount, #loan_product_id', updateRepaymentInfo);
                    } else {
                        // Show onboarding form
                        html += `<form id='onboardCustomerForm'>
                            <div class='alert alert-info'>Customer not onboarded. Please onboard below:</div>
                            <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                            <input type='hidden' name='eligibility' value='${eligibilityAmount}'>
                            <input type='hidden' name='ippis_number' value='${ippis}'>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>IPPIS Number</label>
                                    <input type="text" class="form-control" name="ippis_number" value="${ippis}" required readonly>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>BVN</label>
                                    <input type="text" class="form-control" name="bvn" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                    <label>Account Number</label>
                                    <input type="text" class="form-control" name="account_number" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                    <label>Bank</label>
                                    <select class="form-control" name="bank" id="bankDropdown" required>
                                        <option value="">Select Bank</option>
                                    </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>First Name</label>
                                    <input type="text" class="form-control" name="first_name" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Last Name</label>
                                    <input type="text" class="form-control" name="last_name" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Phone Number</label>
                                    <input type="text" class="form-control" name="phone_number" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Email</label>
                                    <input type="email" class="form-control" name="email" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Next of Kin Name</label>
                                    <input type="text" class="form-control" name="next_of_kin_name" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Next of Kin Phone</label>
                                    <input type="text" class="form-control" name="next_of_kin_phone" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Next of Kin Address</label>
                                    <input type="text" class="form-control" name="next_of_kin_address" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Referee Name</label>
                                    <input type="text" class="form-control" name="referee_name" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Referee Phone</label>
                                    <input type="text" class="form-control" name="referee_phone" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label>Referee Address</label>
                                    <input type="text" class="form-control" name="referee_address" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-success">Onboard Customer</button>
                                </div>
                            </div>
                        </form>`;
                        resultDiv.html(html).show();
                        // Fetch banks for dropdown
                        $.get('<?= base_url('admin/customers/banks') ?>', function(banksRes) {
                            if (Array.isArray(banksRes)) {
                                var options = banksRes.map(function(b) {
                                    return `<option value='${b.name}'>${b.name || b.bank_name}</option>`;
                                });
                                $('#bankDropdown').append(options.join(''));
                            }
                        }, 'json');
                    }
                },
                error: function() {
                    resultDiv.html(eligibilityDisplay + '<div class="alert alert-danger">Could not check customer onboarding status.</div>').show();
                }
            });
        } else {
            resultDiv.html('<div class="alert alert-danger">' + (res.message || 'Customer not found.') + '</div>').show();
        }
            },
            error: function(xhr) {
                btn.prop('disabled', false).text('Check Eligibility');
                resultDiv.html('<div class="alert alert-danger">An error occurred. Please try again.</div>').show();
            }
        });
    });

    // Handle onboarding form submit
    $(document).on('submit', '#onboardCustomerForm', function(e) {
        e.preventDefault();
        var form = $(this);
        var btn = form.find('button[type="submit"]');
        btn.prop('disabled', true).text('Onboarding, please wait...');
        var formData = form.serialize();
        // Add CSRF token
        formData += '&' + encodeURIComponent(csrfName) + '=' + encodeURIComponent(csrfHash);
        $.ajax({
            url: '<?= base_url('admin/wacs/onboardCustomer') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(res) {
                btn.prop('disabled', false).text('Onboard Customer');
                // Remove previous error messages
                form.find('.invalid-feedback').remove();
                form.find('.is-invalid').removeClass('is-invalid');
                // Accept both {customer} and {customer_id} or fallback to res.data
                var customer = (res.data && res.data.customer) ? res.data.customer : null;
                var customer_id = (customer && customer.id) ? customer.id : (res.data && res.data.customer_id ? res.data.customer_id : '');
                // Use value from form if not present in response
                var ippis = (customer && customer.ippis_number) ? customer.ippis_number : (form.find('[name="ippis_number"]').val() || (res.data && res.data.ippis_number ? res.data.ippis_number : ''));
                if (res.success && customer_id) {
                    // Show loan application form for onboarded customer
                    var loanForm = `<form id="loanApplicationForm">
                        <div class='alert alert-success'>Customer onboarded successfully! Now create a loan.</div>
                        <input type='hidden' name='customer_id' value='${customer_id}'>
                        <div class='form-group'>
                            <label>IPPIS Number</label>
                            <input type='text' class='form-control' value='${ippis}' readonly>
                        </div>
                        <div class='form-group'>
                            <label for='loan_product_id'>Loan Product</label>
                            <select class='form-control' name='loan_product_id' id='loan_product_id' required></select>
                        </div>
                        <div class='form-group'>
                            <label for='amount'>Amount</label>
                            <div id="amountWarning" class="alert alert-warning" style="display:none;padding:4px 8px;margin-bottom:6px;font-size:90%"></div>
                            <input type='number' class='form-control' name='amount' id='amount' required>
                            <div id="repaymentInfo" class="mt-2" style="display:none;"></div>
                        </div>
                        <button type='submit' class='btn btn-primary'>Apply for Loan</button>
                    </form>`;
                    form.replaceWith(loanForm);
                    // Fetch loan products
                    $.get('<?= base_url('admin/wacs/getLoanProducts') ?>', function(productsRes) {
                        var productsMap = {};
                        if (productsRes.success && Array.isArray(productsRes.data)) {
                            productsRes.data.forEach(function(p) {
                                productsMap[p.id] = p;
                            });
                            var options = productsRes.data
                                .filter(function(p) { return p.status === 'active' || p.status === 1 || p.is_active === 1; })
                                .map(function(p) {
                                    var rate = p.interest_rate ? p.interest_rate + '%' : '';
                                    var period = p.payback_period ? p.payback_period : (p.tenure ? p.tenure : '');
                                    var label = `${p.name} - ${rate} - ${period}`;
                                    return `<option value='${p.id}'>${label}</option>`;
                                });
                            $('#loan_product_id').html(options.join(''));
                        }
                    }, 'json');
                    // Add warning logic for amount field
                    var eligible = (res.data && res.data.eligibility) ? parseFloat(res.data.eligibility) : null;
                    $(document).off('input.amountWarning').on('input.amountWarning', '#amount', function() {
                        var val = parseFloat($(this).val());
                        if (eligible && val > eligible) {
                            $('#amountWarning').text('Loan amount is greater than eligible amount').show();
                        } else {
                            $('#amountWarning').hide();
                        }
                    });
                    function updateRepaymentInfo() {
                        var productId = $('#loan_product_id').val();
                        var amount = parseFloat($('#amount').val());
                        var infoDiv = $('#repaymentInfo');
                        if (productsMap[productId] && amount > 0) {
                            var rate = parseFloat(productsMap[productId].interest_rate) || 0;
                            var period = parseInt(productsMap[productId].payback_period || productsMap[productId].tenure) || 1;
                            var totalRepayable = amount + (amount * rate * period / 100);
                            var monthlyRepayment = totalRepayable / period;
                            infoDiv.html(
                                `<div class='alert alert-info'>Monthly Repayment: <b>${monthlyRepayment.toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}</b><br>Tenure: <b>${period} month(s)</b><br>Total Repayable: <b>${totalRepayable.toLocaleString(undefined, {minimumFractionDigits:2, maximumFractionDigits:2})}</b></div>`
                            ).show();
                        } else {
                            infoDiv.hide();
                        }
                    }
                    $(document).on('input change', '#amount, #loan_product_id', updateRepaymentInfo);
                } else if (res.success) {
                    form.replaceWith('<div class="alert alert-success">' + (res.message || 'Customer onboarded successfully!') + '</div>');
                } else {
                    // Show per-field errors if available
                    if (res.errors && typeof res.errors === 'object') {
                        Object.keys(res.errors).forEach(function(field) {
                            var errorMsg = res.errors[field];
                            // Map nested error keys to input names
                            var inputName = field;
                            // next_of_kin.name -> next_of_kin_name
                            if (field.startsWith('next_of_kin.')) {
                                inputName = 'next_of_kin_' + field.split('.')[1];
                            }
                            // referee.0.name -> referee_name, referee.0.phone -> referee_phone, etc.
                            else if (/^referee\.\d+\.(name|phone|address)$/.test(field)) {
                                var subField = field.split('.')[2];
                                inputName = 'referee_' + subField;
                            }
                            var input = form.find('[name="' + inputName + '"]');
                            if (input.length) {
                                input.addClass('is-invalid');
                                var parent = input.closest('.form-group');
                                if (!parent.length) parent = input.parent();
                                parent.append('<div class="invalid-feedback" style="display:block">' + errorMsg + '</div>');
                            }
                        });
                    }
                    form.find('.alert').remove();
                    form.prepend('<div class="alert alert-danger">' + (res.message || 'Onboarding failed.') + '</div>');
                }
            },
            error: function(xhr) {
                btn.prop('disabled', false).text('Onboard Customer');
                form.find('.alert').remove();
                form.prepend('<div class="alert alert-danger">An error occurred. Please try again.</div>');
            }
        });
    });

    // Handle loan application form submit
    $(document).on('submit', '#loanApplicationForm', function(e) {
        e.preventDefault();
        var form = $(this);
        var btn = form.find('button[type="submit"]');
        btn.prop('disabled', true).text('Applying, please wait...');
        var data = {
            customer_id: form.find('[name="customer_id"]').val(),
            loan_product_id: form.find('[name="loan_product_id"]').val(),
            amount: form.find('[name="amount"]').val()
        };
        // Add CSRF token
        data[csrfName] = csrfHash;
        $.ajax({
            url: '<?= base_url('admin/wacs/applyLoan') ?>',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            dataType: 'json',
            success: function(res) {
                btn.prop('disabled', false).text('Apply for Loan');
                if (res.success) {
                    form.replaceWith('<div class="alert alert-success">' + (res.message || 'Loan application successful!') + '</div>');
                } else {
                    form.find('.alert').remove();
                    form.prepend('<div class="alert alert-danger">' + (res.message || 'Loan application failed.') + '</div>');
                }
            },
            error: function(xhr) {
                btn.prop('disabled', false).text('Apply for Loan');
                form.find('.alert').remove();
                var msg = 'An error occurred. Please try again.';
                if (xhr && xhr.responseText) {
                    // Try to extract error message from response
                    try {
                        var json = JSON.parse(xhr.responseText);
                        if (json && json.message) msg = json.message;
                    } catch (e) {
                        msg += '<br><small>' + xhr.responseText + '</small>';
                    }
                }
                form.prepend('<div class="alert alert-danger">' + msg + '</div>');
            }
        });
    });
});
</script>
