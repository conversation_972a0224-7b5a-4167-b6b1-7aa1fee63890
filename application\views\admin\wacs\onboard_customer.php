<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Onboard New Customer</h1>
                </div>
            </div>
        </div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <form id="onboardCustomerForm">
                                <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                                <div class="form-group">
                                    <label for="first_name">First Name</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                    <div class="invalid-feedback" id="error_first_name"></div>
                                </div>
                                <div class="form-group">
                                    <label for="last_name">Last Name</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                    <div class="invalid-feedback" id="error_last_name"></div>
                                </div>
                                <div class="form-group">
                                    <label for="email">Email</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                    <div class="invalid-feedback" id="error_email"></div>
                                </div>
                                <div class="form-group">
                                    <label for="phone">Phone</label>
                                    <input type="text" class="form-control" id="phone" name="phone" required>
                                    <div class="invalid-feedback" id="error_phone"></div>
                                </div>
                                <div class="form-group">
                                    <label for="ippis_number">IPPIS Number</label>
                                    <input type="text" class="form-control" id="ippis_number" name="ippis_number" required>
                                    <div class="invalid-feedback" id="error_ippis_number"></div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group col-md-6">
                                        <label for="account_number">Account Number</label>
                                        <input type="text" class="form-control" id="account_number" name="account_number" required>
                                        <div class="invalid-feedback" id="error_account_number"></div>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="bank">Bank</label>
                                        <select class="form-control" id="bank" name="bank" required>
                                            <option value="">Select Bank</option>
                                        </select>
                                        <div class="invalid-feedback" id="error_bank"></div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">Onboard Customer</button>
                            </form>
                            <div id="onboardResult" class="mt-4" style="display:none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
$(document).ready(function() {
    $('#onboardCustomerForm').on('submit', function(e) {
        e.preventDefault();
        var formData = $(this).serialize();
        $('#onboardResult').hide().html('');
        $('.invalid-feedback').html('').hide();
        $('.form-control').removeClass('is-invalid');
        $.ajax({
            url: base_url + 'admin/wacs/onboardCustomer',
            method: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('#onboardResult').html('<div class="alert alert-info">Onboarding customer...</div>').show();
            },
            success: function(res) {
                if (res.success) {
                    $('#onboardResult').html('<div class="alert alert-success">' + (res.message || 'Customer onboarded successfully!') + '</div>').show();
                } else {
                    // Try to parse per-field errors if present
                    if (res.errors && typeof res.errors === 'object') {
                        $.each(res.errors, function(field, messages) {
                            var msg = Array.isArray(messages) ? messages.join('<br>') : messages;
                            $('#error_' + field).html(msg).show();
                            $('#' + field).addClass('is-invalid');
                        });
                    }
                    $('#onboardResult').html('<div class="alert alert-warning">' + (res.message || 'Onboarding failed.') + '</div>').show();
                }
            },
            error: function(xhr) {
                var msg = 'An error occurred while onboarding customer.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    msg = xhr.responseJSON.message;
                }
                $('#onboardResult').html('<div class="alert alert-danger">' + msg + '</div>').show();
            }
        });
    });
});
</script>
