<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-tachometer-alt"></i> WACS Dashboard</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('admin') ?>">Home</a></li>
                        <li class="breadcrumb-item active">WACS Dashboard</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Stats Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3><?php echo isset($stats['customers']) ? number_format($stats['customers']) : 0; ?></h3>
                            <p>Total Customers</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <a href="<?= base_url('admin/wacs/listOnboardedCustomers') ?>" class="small-box-footer">
                            More info <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3><?php echo isset($stats['loans']) ? number_format($stats['loans']) : 0; ?></h3>
                            <p>Active Loans</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <a href="<?= base_url('admin/wacs/allLoans') ?>" class="small-box-footer">
                            More info <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>₦<?php echo isset($stats['total_disbursed']) ? number_format($stats['total_disbursed'], 0) : '0'; ?></h3>
                            <p>Total Disbursed</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <a href="#" class="small-box-footer">
                            View Details <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>₦<?php echo isset($stats['total_repaid']) ? number_format($stats['total_repaid'], 0) : '0'; ?></h3>
                            <p>Total Repaid</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <a href="#" class="small-box-footer">
                            View Details <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-bolt"></i> Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/createLoanForm') ?>" class="btn btn-app bg-success">
                                        <i class="fas fa-plus"></i> New Loan
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/createWacsCustomerForm') ?>" class="btn btn-app bg-info">
                                        <i class="fas fa-user-plus"></i> Onboard Customer
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/check_eligibility') ?>" class="btn btn-app bg-warning">
                                        <i class="fas fa-calculator"></i> Check Eligibility
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/createLoanProductForm') ?>" class="btn btn-app bg-primary">
                                        <i class="fas fa-cog"></i> Loan Product
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/listLoanProducts') ?>" class="btn btn-app bg-secondary">
                                        <i class="fas fa-list"></i> All Products
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('admin/wacs/allLoans') ?>" class="btn btn-app bg-dark">
                                        <i class="fas fa-file-alt"></i> All Loans
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Recent Loans -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-clock"></i> Recent Loans</h3>
                            <div class="card-tools">
                                <div class="input-group input-group-sm" style="width: 250px;">
                                    <input type="text" id="loanSearch" class="form-control float-right" placeholder="Search by name, IPPIS, or account">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="recentLoansContainer">
                                <?php if (!empty($recent_loans)) : ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover mb-0">
                                            <thead class="thead-light">
                                                <tr>
                                                    <th>Loan ID</th>
                                                    <th>Customer</th>
                                                    <th>IPPIS</th>
                                                    <th>Product</th>
                                                    <th>Amount</th>
                                                    <th>Disbursed</th>
                                                    <th>Status</th>
                                                    <th>Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="loansTableBody">
                                                <?php foreach ($recent_loans as $loan) : ?>
                                                    <tr class="loan-row" data-search="<?= strtolower(htmlspecialchars($loan['employee_name'] ?? $loan['debtor'] ?? '') . ' ' . ($loan['customer_ippis'] ?? '') . ' ' . ($loan['account_number'] ?? '')) ?>">
                                                        <td>
                                                            <span class="badge badge-primary">#<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?></span>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-2" style="width: 35px; height: 35px; font-size: 14px;">
                                                                    <?= strtoupper(substr($loan['employee_name'] ?? $loan['debtor'] ?? 'N', 0, 2)) ?>
                                                                </div>
                                                                <div>
                                                                    <div class="font-weight-bold"><?= htmlspecialchars($loan['employee_name'] ?? $loan['debtor'] ?? 'N/A') ?></div>
                                                                    <small class="text-muted"><?= htmlspecialchars($loan['customer_email'] ?? '') ?></small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="font-weight-bold"><?= htmlspecialchars($loan['customer_ippis'] ?? 'N/A') ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="text-info"><?= htmlspecialchars($loan['loan_product'] ?? 'N/A') ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="font-weight-bold text-success">₦<?= number_format($loan['amount_requested'] ?? 0, 2) ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="font-weight-bold text-primary">₦<?= number_format($loan['disbursed_amount'] ?? 0, 2) ?></span>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $status = $loan['status_loan'] ?? $loan['status'] ?? 'pending';
                                                            $badgeClass = $status === 'approved' ? 'success' : ($status === 'pending' ? 'warning' : ($status === 'rejected' ? 'danger' : 'info'));
                                                            ?>
                                                            <span class="badge badge-<?= $badgeClass ?> px-2 py-1">
                                                                <i class="fas fa-<?= $status === 'approved' ? 'check' : ($status === 'pending' ? 'clock' : ($status === 'rejected' ? 'times' : 'info')) ?>"></i>
                                                                <?= ucfirst(htmlspecialchars($status)) ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="text-muted"><?= date('M d, Y', strtotime($loan['start_date'] ?? 'now')) ?></span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <button type="button" class="btn btn-sm btn-outline-primary loan-action-btn"
                                                                        data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                                                        data-action="view" title="View Details">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-outline-success loan-action-btn"
                                                                        data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                                                        data-action="approve" title="Approve/Reject">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-outline-warning loan-action-btn"
                                                                        data-loan-id="<?= htmlspecialchars($loan['loan_id'] ?? $loan['id']) ?>"
                                                                        data-action="liquidate" title="Liquidate">
                                                                    <i class="fas fa-money-bill"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else : ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No recent loans found</h5>
                                        <p class="text-muted">Start by creating a new loan application</p>
                                        <a href="<?= base_url('admin/wacs/createLoanForm') ?>" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Create New Loan
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php if (!empty($recent_loans)) : ?>
                        <div class="card-footer">
                            <div class="row">
                                <div class="col-sm-6">
                                    <small class="text-muted">Showing <?= count($recent_loans) ?> recent loans</small>
                                </div>
                                <div class="col-sm-6 text-right">
                                    <a href="<?= base_url('admin/wacs/allLoans') ?>" class="btn btn-sm btn-outline-primary">
                                        View All Loans <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Modal for Approve/Reject/Liquidate -->
    <div class="modal fade" id="loanActionModal" tabindex="-1" role="dialog" aria-labelledby="loanActionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="loanActionModalLabel">
                        <i class="fas fa-cogs"></i> Loan Action
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="loanActionForm">
                        <?php echo form_hidden($this->security->get_csrf_token_name(), $this->security->get_csrf_hash()); ?>
                        <input type="hidden" name="loan_id" id="modal_loan_id">

                        <div class="form-group">
                            <label for="action_type"><i class="fas fa-tasks"></i> Action</label>
                            <select class="form-control" id="action_type" name="action_type" required>
                                <option value="">Select Action</option>
                                <option value="approve">Approve</option>
                                <option value="reject">Reject</option>
                                <option value="liquidate">Liquidate</option>
                            </select>
                        </div>

                        <div class="form-group" id="liquidate_note_group" style="display:none;">
                            <label for="liquidate_note"><i class="fas fa-sticky-note"></i> Liquidation Note</label>
                            <textarea class="form-control" id="liquidate_note" name="liquidate_note" rows="3" placeholder="Enter liquidation details..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="action_note"><i class="fas fa-comment"></i> Additional Note (optional)</label>
                            <textarea class="form-control" id="action_note" name="action_note" rows="3" placeholder="Enter any additional notes..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" form="loanActionForm" class="btn btn-primary">
                        <i class="fas fa-check"></i> Submit Action
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Search functionality
    $('#loanSearch').on('keyup', function() {
        var searchTerm = $(this).val().toLowerCase();
        $('#loansTableBody tr.loan-row').each(function() {
            var searchData = $(this).data('search');
            if (searchData.indexOf(searchTerm) > -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });

        // Show/hide "no results" message
        var visibleRows = $('#loansTableBody tr.loan-row:visible').length;
        if (visibleRows === 0 && searchTerm !== '') {
            if ($('#noResultsRow').length === 0) {
                $('#loansTableBody').append('<tr id="noResultsRow"><td colspan="9" class="text-center py-4"><i class="fas fa-search"></i> No loans found matching your search.</td></tr>');
            }
        } else {
            $('#noResultsRow').remove();
        }
    });

    // Loan action modal
    $(document).on('click', '.loan-action-btn', function() {
        var loanId = $(this).data('loan-id');
        var action = $(this).data('action');

        $('#modal_loan_id').val(loanId);
        $('#action_type').val(action === 'approve' ? '' : action);
        $('#liquidate_note_group').toggle(action === 'liquidate');

        // Update modal title based on action
        var actionText = action === 'view' ? 'View Loan' : (action === 'approve' ? 'Approve/Reject Loan' : 'Liquidate Loan');
        $('#loanActionModalLabel').html('<i class="fas fa-cogs"></i> ' + actionText);

        $('#loanActionModal').modal('show');
    });

    // Toggle liquidation note field
    $('#action_type').on('change', function() {
        $('#liquidate_note_group').toggle($(this).val() === 'liquidate');
    });

    // Handle form submission
    $('#loanActionForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitBtn = $('button[type="submit"][form="loanActionForm"]');
        var originalText = submitBtn.html();

        // Show loading state
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

        var data = form.serialize();

        $.ajax({
            url: '<?= base_url('admin/wacs/loanAction') ?>',
            method: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#loanActionModal').modal('hide');

                    // Show success message
                    toastr.success(response.message || 'Action completed successfully!');

                    // Reload page after short delay
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    toastr.error(response.message || 'Action failed. Please try again.');
                }
            },
            error: function() {
                toastr.error('An error occurred. Please try again.');
            },
            complete: function() {
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
