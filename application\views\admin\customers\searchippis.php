<!-- DataTables -->
  <!-- Content Wrapper. Contains page content -->
  <style type="text/css">
    .staff-info{
      list-style-type: none;
    }
    .staff-info .title{
      font-weight: 700;
    }

    .staff-info li{
      border-bottom: 1px solid lightgray;
      padding: 10px 0px ;
    }

  </style>
<div class="content-wrapper">
    <!-- Main content -->
    <section class="content">
        
      <div class="container">
          <div class="row">
              <div class="col-md-12 mt-5">
                 <h5 class="mb-3">Search IPPIS Data</h5>
                 <div class="input-group input-group-lg">
                  <input type="text" class="form-control" id="search" placeholder="Enter Phone No, Account No or IPPIS No" value="">
                
                </div>

                <div id="loading-container">
                  <img src="<?php echo base_url().'assets/dist/img/loading.gif' ?>"> Searching ippis data. please wait...
                </div>
                <span class="val-msg mt-1 ml-1"></span>
                <div class="row">
                    <div class="col-md-12 mt-1">
                     

                      <div class="result card mt-4" style="display: none;">
                         <div class="card-body">
                            <dl class="row">
                               <div id="search-results"></div>
                               
                            </dl>
                            <!-- <a href="" id="salarydetails" class="btn btn-warning btn-sm">Salary Details</a> -->
                         </div>
                         
                        
                      </div>
                     
                  </div>
                </div>
              </div>
            

             
          </div>
      </div>
    <!-- /.box -->
  </section>  
</div>

<script>
    $(document).ready(function() {
        $('#search').keyup(function() {
            var search_query = $(this).val();
            if (search_query.length > 3) {
                $.ajax({
                    url: '<?=base_url("admin/customers/getIppisData")?>',
                    method: 'POST',
                    data: {search_query: search_query,'<?php echo $this->security->get_csrf_token_name(); ?>' : '<?php echo $this->security->get_csrf_hash(); ?>'},
                    dataType: 'json',
                    success: function(data) {
                        $('#search-results').html('');
                        
                        if (data.length === 0) {
                             $('.val-msg').html('<i class="fa fa-times"></i>No results found').removeClass('text-danger').addClass('text-danger');
                        } else {
                            $('.val-msg').html('<i class="fa fa-check"></i> Yay! Staff was found').removeClass('text-danger').addClass('text-success');
                             $(".result").show();
                             var ul = $('<ul class="staff-info">');
                              $.each(data, function(index, value) {
                                  ul.append('<li><span class="title">Name:</span> ' + value.name + '</li>');
                                  ul.append('<li><span class="title">Phone:</span> ' + value.phone + '</li>');
                                  ul.append('<li><span class="title">IPPIS No:</span> ' + value.ippisno + '</li>');
                                  ul.append('<li><span class="title">Account No:</span> ' + value.accountno + '</li>');
                                  ul.append('<li><span class="title">Bank:</span> ' + value.bank + '</li>');
                                  ul.append('<li><span class="title">Office:</span> ' + value.ministry + '</li>');
                              });
                              $('#search-results').append(ul);
                            
                        }
                    }
                });
            } else {
                $('#search-results').html('');
                $('.val-msg').html('');
                $(".result").hide();
            }
        });
    });
</script>

<!-- DataTables -->

     