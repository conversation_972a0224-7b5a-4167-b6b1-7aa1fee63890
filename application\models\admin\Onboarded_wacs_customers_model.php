<?php
class Onboarded_wacs_customers_model extends CI_Model {

    public function insert_onboarded_customer($data) {
        $this->db->insert('onboarded_wacs_customers', $data);
        return $this->db->insert_id();
    }

    public function update_eligibility_by_ippis($ippis_number, $eligible_amount) {
        $this->db->where('ippis_number', $ippis_number);
        return $this->db->update('onboarded_wacs_customers', ['eligibility' => $eligible_amount]);
    }

    public function update_by_ippis($ippis_number, $data) {
        $this->db->where('ippis_number', $ippis_number);
        return $this->db->update('onboarded_wacs_customers', $data);
    }

    public function get_by_ippis($ippis_number) {
        return $this->db->get_where('onboarded_wacs_customers', ['ippis_number' => $ippis_number])->row_array();
    }

    public function get_by_customer_id($customer_id) {
        return $this->db->get_where('onboarded_wacs_customers', ['customer_id' => $customer_id])->row_array();
    }
}
