
<div class="login-page">
    <div class="login-box">
        <div class="card card-outline card-primary">
            <div class="card-header text-center">
                <div class="login-logo">
                    <img src="<?= base_url($this->general_settings['logo']); ?>" alt="Logo" style="">
                </div>
                <h3 class="h4 mt-3 mb-0"><b><?= $this->general_settings['application_name']; ?></b></h3>
                <p class="login-box-msg text-white">Sign in to start your session</p>
            </div>
            <div class="card-body">
                <?php $this->load->view('admin/includes/_messages.php') ?>

                <?php echo form_open(base_url('admin/auth/login'), 'class="login-form" id="loginForm"'); ?>
                    <div class="input-group mb-3">
                        <input type="text" name="username" id="username" class="form-control form-control-lg"
                               placeholder="<?= trans('username') ?>" required autofocus>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-user"></span>
                            </div>
                        </div>
                    </div>

                    <div class="input-group mb-3">
                        <input type="password" name="password" id="password" class="form-control form-control-lg"
                               placeholder="<?= trans('password') ?>" required>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-lock"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <div class="icheck-primary">
                                <input type="checkbox" id="remember" name="remember_me" value="1">
                                <label for="remember">
                                    Remember Me
                                </label>
                            </div>
                        </div>
                        <div class="col-6">
                            <button type="submit" class="btn btn-primary btn-block btn-lg">
                                Sign In
                            </button>
                        </div>
                    </div>
                <?php echo form_close(); ?>

                <div class="text-center">
                    <p class="mb-1">
                        <a href="<?= base_url('admin/auth/forget_password') ?>" class="text-primary">
                            <i class="fas fa-key"></i> I forgot my password
                        </a>
                    </p>
                    <p class="mb-0">
                        <a href="<?= base_url('wacs/register') ?>" class="text-success">
                            <i class="fas fa-user-plus"></i> WACS Registration
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
$(document).ready(function() {
    // Enhanced login form with proper validation
    $('#loginForm').on('submit', function(e) {
        // Clear previous validation errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        var isValid = true;
        var username = $('#username').val().trim();
        var password = $('#password').val().trim();

        // Validate username
        if (!username) {
            showFieldError('username', 'Username is required.');
            isValid = false;
        }

        // Validate password
        if (!password) {
            showFieldError('password', 'Password is required.');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            return false;
        }

        // Show loading state
        var btn = $(this).find('button[type="submit"]');
        var originalText = btn.html();
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Signing In...');

        // Allow form to submit normally
        // The loading state will be reset by page reload or by timeout
        setTimeout(function() {
            if (btn.length) {
                btn.prop('disabled', false).html(originalText);
            }
        }, 5000);
    });

    // Function to show field validation errors
    function showFieldError(fieldId, message) {
        var field = $('#' + fieldId);
        field.addClass('is-invalid');

        // Add error message
        var errorDiv = $('<div class="invalid-feedback d-block">' + message + '</div>');
        field.closest('.input-group').after(errorDiv);
    }

    // Clear validation errors when user starts typing
    $('#username, #password').on('input', function() {
        $(this).removeClass('is-invalid');
        $(this).closest('.input-group').siblings('.invalid-feedback').remove();
    });

    // Auto-focus username field
    $('#username').focus();
});
</script>

<style>
.login-page {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-box {
    width: 400px;
    margin: 0 auto;
}

.card {
    background: white;
    box-shadow: 0 10px 30px rgba(21, 53, 130, 0.15);
    border: 1px solid rgba(21, 53, 130, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    background: #153582;
    color: white;
    border-bottom: none;
    padding: 2rem 1.5rem 1.5rem;
}


.card-body {
    background: white;
    padding: 2rem;
}

.input-group-text {
    background: #f8f9fa;
    border: 1px solid #ced4da;
    color: #153582;
}

.form-control-lg {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
}

.form-control-lg:focus {
    border-color: #153582;
    box-shadow: 0 0 0 0.2rem rgba(21, 53, 130, 0.25);
}

.btn-primary {
    background: #153582;
    border: 1px solid #153582;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #0f2a6b;
    border-color: #0f2a6b;
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(21, 53, 130, 0.3);
}

.btn-primary:focus {
    background: #0f2a6b;
    border-color: #0f2a6b;
    box-shadow: 0 0 0 0.2rem rgba(21, 53, 130, 0.25);
}

.icheck-primary input[type="checkbox"]:checked + label::before {
    background-color: #153582;
    border-color: #153582;
}

.text-primary {
    color: #153582 !important;
}

.text-primary:hover {
    color: #0f2a6b !important;
}

.text-success {
    color: #28a745 !important;
}

.modal-header.bg-warning {
    background: #ffc107 !important;
    color: #212529;
}

.modal-header.bg-success {
    background: #28a745 !important;
    color: white;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

@media (max-width: 576px) {
    .login-box {
        width: 90%;
        margin: 1rem;
    }

    .card-header {
        padding: 1.5rem 1rem 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }
}
</style>
