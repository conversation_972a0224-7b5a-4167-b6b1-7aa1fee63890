
<div class="login-page">
    <div class="login-box">
        <div class="card card-outline card-primary">
            <div class="card-header text-center">
                <div class="login-logo">
                    <img src="<?= base_url($this->general_settings['logo']); ?>" alt="Logo" class="brand-image img-circle elevation-3" style="max-height: 80px; opacity: .8">
                </div>
                <h3 class="h4 mt-3 mb-0"><b><?= $this->general_settings['application_name']; ?></b></h3>
                <p class="login-box-msg text-muted">Sign in to start your session</p>
            </div>
            <div class="card-body">
                <?php $this->load->view('admin/includes/_messages.php') ?>

                <?php echo form_open(base_url('admin/auth/login'), 'class="login-form" id="loginForm"'); ?>
                    <div class="input-group mb-3">
                        <input type="text" name="username" id="username" class="form-control form-control-lg"
                               placeholder="<?= trans('username') ?>" required autofocus>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-user"></span>
                            </div>
                        </div>
                    </div>

                    <div class="input-group mb-3">
                        <input type="password" name="password" id="password" class="form-control form-control-lg"
                               placeholder="<?= trans('password') ?>" required>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-lock"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-8">
                            <div class="icheck-primary">
                                <input type="checkbox" id="remember" name="remember_me" value="1">
                                <label for="remember">
                                    Remember Me
                                </label>
                            </div>
                        </div>
                        <div class="col-4">
                            <button type="submit" class="btn btn-primary btn-block btn-lg">
                                <i class="fas fa-sign-in-alt"></i> Sign In
                            </button>
                        </div>
                    </div>
                <?php echo form_close(); ?>

                <div class="text-center">
                    <p class="mb-1">
                        <a href="#" id="forgotPasswordLink" class="text-primary">
                            <i class="fas fa-key"></i> I forgot my password
                        </a>
                    </p>
                    <p class="mb-0">
                        <a href="<?= base_url('wacs/register') ?>" class="text-success">
                            <i class="fas fa-user-plus"></i> WACS Registration
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Forgot Password Modal -->
<div class="modal fade" id="forgotPasswordModal" tabindex="-1" role="dialog" aria-labelledby="forgotPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="forgotPasswordModalLabel">
                    <i class="fas fa-key"></i> Reset Password
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Password Reset:</strong> Enter your username or email address and we'll send you a link to reset your password.
                </div>

                <form id="forgotPasswordForm">
                    <div class="form-group">
                        <label for="reset_identifier">
                            <i class="fas fa-user"></i> Username or Email Address
                        </label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-at"></i></span>
                            </div>
                            <input type="text" class="form-control" id="reset_identifier" name="identifier"
                                   placeholder="Enter your username or email" required>
                        </div>
                    </div>
                </form>

                <div id="resetResult" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="submit" form="forgotPasswordForm" class="btn btn-warning">
                    <i class="fas fa-paper-plane"></i> Send Reset Link
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" role="dialog" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="resetPasswordModalLabel">
                    <i class="fas fa-lock"></i> Set New Password
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-shield-alt"></i>
                    <strong>Security:</strong> Please choose a strong password with at least 8 characters.
                </div>

                <form id="resetPasswordForm">
                    <input type="hidden" id="reset_token" name="token">

                    <div class="form-group">
                        <label for="new_password">
                            <i class="fas fa-lock"></i> New Password
                        </label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                            </div>
                            <input type="password" class="form-control" id="new_password" name="password"
                                   placeholder="Enter new password" required minlength="8">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">
                            <i class="fas fa-lock"></i> Confirm Password
                        </label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-check"></i></span>
                            </div>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                   placeholder="Confirm new password" required minlength="8">
                        </div>
                    </div>
                </form>

                <div id="resetPasswordResult" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="submit" form="resetPasswordForm" class="btn btn-success">
                    <i class="fas fa-save"></i> Update Password
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Handle forgot password link
    $('#forgotPasswordLink').on('click', function(e) {
        e.preventDefault();
        $('#forgotPasswordModal').modal('show');
    });

    // Handle forgot password form submission
    $('#forgotPasswordForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.html();

        // Show loading state
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Sending...');

        var identifier = $('#reset_identifier').val().trim();

        if (!identifier) {
            showResetResult('error', 'Please enter your username or email address.');
            btn.prop('disabled', false).html(originalText);
            return;
        }

        $.ajax({
            url: '<?= base_url('admin/auth/forgotPassword') ?>',
            method: 'POST',
            data: { identifier: identifier },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showResetResult('success', response.message || 'Password reset link sent successfully!');
                    form[0].reset();
                } else {
                    showResetResult('error', response.message || 'Failed to send reset link.');
                }
            },
            error: function() {
                showResetResult('error', 'An error occurred. Please try again.');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle reset password form submission
    $('#resetPasswordForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var btn = form.find('button[type="submit"]');
        var originalText = btn.html();

        var password = $('#new_password').val();
        var confirmPassword = $('#confirm_password').val();

        if (password !== confirmPassword) {
            showResetPasswordResult('error', 'Passwords do not match.');
            return;
        }

        if (password.length < 8) {
            showResetPasswordResult('error', 'Password must be at least 8 characters long.');
            return;
        }

        // Show loading state
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');

        var formData = form.serialize();

        $.ajax({
            url: '<?= base_url('admin/auth/resetPassword') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showResetPasswordResult('success', response.message || 'Password updated successfully!');
                    form[0].reset();
                    setTimeout(function() {
                        $('#resetPasswordModal').modal('hide');
                        location.reload();
                    }, 2000);
                } else {
                    showResetPasswordResult('error', response.message || 'Failed to update password.');
                }
            },
            error: function() {
                showResetPasswordResult('error', 'An error occurred. Please try again.');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Toggle password visibility
    $('#togglePassword').on('click', function() {
        var passwordField = $('#new_password');
        var icon = $(this).find('i');

        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Show reset result
    function showResetResult(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

        var html = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="${icon}"></i> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        $('#resetResult').html(html).show();
    }

    // Show reset password result
    function showResetPasswordResult(type, message) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

        var html = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="${icon}"></i> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        $('#resetPasswordResult').html(html).show();
    }

    // Check for reset token in URL
    var urlParams = new URLSearchParams(window.location.search);
    var resetToken = urlParams.get('token');
    if (resetToken) {
        $('#reset_token').val(resetToken);
        $('#resetPasswordModal').modal('show');
    }

    // Enhanced login form
    $('#loginForm').on('submit', function(e) {
        var btn = $(this).find('button[type="submit"]');
        var originalText = btn.html();

        // Show loading state
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Signing In...');

        // Form will submit normally, but we show loading state
        setTimeout(function() {
            if (btn.length) {
                btn.prop('disabled', false).html(originalText);
            }
        }, 3000);
    });

    // Auto-focus username field
    $('#username').focus();
});
</script>

<style>
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-box {
    width: 400px;
    margin: 0 auto;
}

.card {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    padding: 2rem 1.5rem 1.5rem;
}

.login-logo img {
    background: white;
    padding: 10px;
    border-radius: 50%;
}

.card-body {
    padding: 2rem;
}

.input-group-text {
    background: #f8f9fa;
    border: 1px solid #ced4da;
    color: #6c757d;
}

.form-control-lg {
    border-radius: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.icheck-primary input[type="checkbox"]:checked + label::before {
    background-color: #667eea;
    border-color: #667eea;
}

.text-primary {
    color: #667eea !important;
}

.text-success {
    color: #28a745 !important;
}

.modal-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%) !important;
    color: white;
}

.modal-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white;
}

@media (max-width: 576px) {
    .login-box {
        width: 90%;
        margin: 1rem;
    }

    .card-header {
        padding: 1.5rem 1rem 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }
}
</style>
