<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Onboarded WACS Customers</h1>
                </div>
            </div>
        </div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-body">
                    <form id="searchForm" class="form-inline mb-3">
                        <input type="text" class="form-control mb-2 mr-sm-2" id="search_query" placeholder="Search by IPPIS, Name, Phone, Account No">
                        <input type="date" class="form-control mb-2 mr-sm-2" id="date_from" placeholder="From">
                        <input type="date" class="form-control mb-2 mr-sm-2" id="date_to" placeholder="To">
                        <button type="submit" class="btn btn-primary mb-2">Search</button>
                    </form>
                    <div id="customersTableContainer">
                        <div class="text-center py-4"><span class="spinner-border"></span> Loading...</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
function loadCustomers(page=1) {
    var q = $('#search_query').val();
    var date_from = $('#date_from').val();
    var date_to = $('#date_to').val();
    $('#customersTableContainer').html('<div class="text-center py-4"><span class="spinner-border"></span> Loading...</div>');
    $.get('<?= base_url('admin/wacs/ajaxListOnboardedCustomers') ?>', {
        page: page,
        q: q,
        date_from: date_from,
        date_to: date_to
    }, function(html) {
        $('#customersTableContainer').html(html);
    });
}
$(function(){
    loadCustomers();
    $('#searchForm').on('submit', function(e){
        e.preventDefault();
        loadCustomers();
    });
    $(document).on('click', '.pagination a', function(e){
        e.preventDefault();
        var page = $(this).data('ci-pagination-page') || $(this).attr('data-page') || $(this).text();
        loadCustomers(page);
    });
});
</script>
