<!-- Content Wrapper. Contains page content -->
<div
	class="content-wrapper">
	<!-- Content Header (Page header) -->
	<div class="content-header">
		<div class="container-fluid">
			<div class="row mb-2">
				<div class="col-sm-6">
					<h1 class="m-0">
						<i class="fas fa-user-shield text-primary"></i>
						Welcome, <span class="text-primary"><?php echo $this->session->userdata('username') ?></span>
					</h1>
					<p class="text-muted mb-0">Administrator Dashboard - Manage system operations</p>
				</div>
				<!-- /.col -->
				<div class="col-sm-6">
					<ol class="breadcrumb float-sm-right">
						<li class="breadcrumb-item">
							<a href="#" class="text-primary"><?= trans('home') ?></a>
						</li>
						<li class="breadcrumb-item active">Admin Dashboard</li>
					</ol>
				</div>
				<!-- /.col -->
			</div>
			<!-- /.row -->
		</div>
		<!-- /.container-fluid -->
	</div>
	<!-- /.content-header -->

	<!-- Main content -->
	<section class="content">
		<div
			class="container-fluid">
			<!-- Info boxes -->
			<div class="row mb-4">
				<div class="col-12 col-sm-6 col-md-3">
					<div class="info-box bg-gradient-success">
						<span class="info-box-icon">
							<i class="fas fa-wallet"></i>
						</span>
						<div class="info-box-content">
							<span class="info-box-text">Wallet Balance</span>
							<span class="info-box-number" id="wallet_balance"><?php echo formatMoney(wallet_balance()); ?></span>
							<div class="progress">
								<div class="progress-bar bg-white" style="width: 70%; opacity: 0.3;"></div>
							</div>
							<span class="progress-description">Available funds</span>
						</div>
					</div>
					<div class="mt-2">
						<a href="<?php echo base_url('admin/transactions/fund_wallet') ?>" class="btn btn-success btn-sm" data-toggle="ajax-modal">
							<i class="fas fa-plus"></i> Fund Wallet
						</a>
						<a href="<?php echo base_url('admin/transactions') ?>" class="btn btn-outline-primary btn-sm float-right">
							<i class="fas fa-list"></i> Statement
						</a>
					</div>
				</div>

				<div class="col-12 col-sm-6 col-md-3">
					<div class="info-box mb-3">
						<span class="info-box-icon bg-danger elevation-1">
							<i class="fa fa-briefcase"></i>
						</span>

						<div class="info-box-content">
							<span class="info-box-text">Cost
								<span
									class="badge badge-warning float-right">
									<?php echo countwhere('customer_successful_search') ?>
									Searches</span>
							</span>
							<span class="info-box-number"><?php echo formatMoney(countwhere('customer_successful_search') * 100) ?></span>
						</div>
						<!-- /.info-box-content -->
					</div>
					<!-- /.info-box -->
				</div>
				<!-- /.col -->

				<!-- fix for small devices only -->
				<div class="clearfix hidden-md-up"></div>

				<div class="col-12 col-sm-6 col-md-3">
					<div class="info-box mb-3">
						<span class="info-box-icon bg-success elevation-1">
							<i class="fa fa-users"></i>
						</span>

						<div class="info-box-content">
							<span class="info-box-text">Remita DRF Customers</span>
							<span class="info-box-number"><?php echo countwhere('customers', ['onremita' => 1]) ?></span>
						</div>
						<!-- /.info-box-content -->
					</div>
					<!-- /.info-box -->
				</div>
				<!-- /.col -->
				<div class="col-12 col-sm-6 col-md-3"></div>
				<!-- /.col -->
			</div>
			<!-- /.row -->


			<!-- Main row -->
			<div
				class="row">
				<!-- Left col -->
				<div class="col-md-4">
					<h5 class="mt-2 mb-2">Recent Customers</h5>

					<?php foreach ($customers as $customer): ?>
						<?php
						$no_of_searchs = countwhere('customer_successful_search', ['customerid' => $customer->remitacustomerid]);
						$salary = !empty(getby(['phone' => $customer->phone], 'customer_salary_loan')) ? getby(['phone' => $customer->phone], 'customer_salary_loan') : null;
						$sal = !empty($salary) ? json_decode($salary->salary) : null;
						?>

						<div class="card border-1 border-secondary rounded-3">
							<div class="card-body">
								<h5 class="card-title collateral-name">
									<a href="<?php echo base_url('admin/customers/salary_loan_details/' . $customer->phone . '?cid=' . $customer->remitacustomerid) ?>" class="d-block"><?php echo $customer->fullname ?></a>
									<small class="d-block">
										<b>Office:</b>
										<?php echo $customer->organization ?>
									</small>
								</h5>
								<div class="card-text collateral-desc">

									<div>
										<b>Salary:
										</b>
										<?php echo !empty($sal) ? formatMoney($sal[0]->amount) : '<span class="badge badge-danger"> no data </span>'; ?>
										<span
											class="badge badge-warning">
											<?php echo $no_of_searchs ?>
											<i class="fa fa-search"></i>
										</span>

										<?php if ($customer->onremita == 1): ?>
											<span class="badge badge-success">on remita</span>
										<?php else: ?>
											<span class="badge badge-danger">not on remita</span>
										<?php endif ?>
										<div>
											<small
												class="text-muted"><?php echo formatDate($customer->lastchecked); ?>
											</small>
										</div>
									</div>

								</div>
								<div class="float-right text-sm">

									<div class="float-right mb-2">
										<a href="<?php echo base_url('admin/customers/salary_loan_details/' . $customer->phone . '?cid=' . $customer->remitacustomerid) ?>" class="btn btn-primary btn-sm">Details</a>
									</div>
									<div style="clear: both;"></div>


								</div>

							</div>
						</div>
					<?php endforeach ?>


				<!-- TABLE: LATEST ORDERS -->

					<!-- /.card -->
				</div>
				<!-- /.col -->

				<div
					class="col-md-4 pt-5"><?php $this->load->view('admin/customers/recentsalarysearch') ?>

				</div>
				<div
					class="col-md-4 pt-5">

					<?php $loans = getalldata('loans', null, 'id', 'DESC', 0, 4); ?>
					<div class="card">
						<div class="card-header">
							<h3 class="card-title">Recent Loans</h3>

							<div class="card-tools">
								<button type="button" class="btn btn-tool" data-widget="collapse">
									<i class="fa fa-minus"></i>
								</button>
								<button type="button" class="btn btn-tool" data-widget="remove">
									<i class="fa fa-times"></i>
								</button>
							</div>
						</div>
						<!-- /.card-header -->
						<div class="card-body p-0">
							<ul class="products-list product-list-in-card pl-2 pr-2">
								<?php foreach ($loans as $loan): ?>
									<?php
									$customer_successful_search = empty(getby(['customerid' => $loan->customerid], 'customer_successful_search')) ? false : getby(['customerid' => $loan->customerid], 'customer_successful_search');

									$customerData = !empty($customer_successful_search->response) ? json_decode($customer_successful_search->response)->data : null;

									?>

										<li class="item"> <div class="product-info">
											<a
												href="#">
												<?php echo !empty($customerData->customerName) ? ucwords(strtolower($customerData->customerName)) : '<span ="text-danger">no name</span>' ?>
												<span class="badge badge-info float-right"><?php echo $loan->bookedwith ?></span>
											</a>
											<br>


											<small>
												<span class="d-block">
													<b>Man.  Ref:</b>
													<?php echo empty($loan->mandateref) ? 'none' : $loan->mandateref . ' <span class="fa fa-check-circle text-success"></span>' ?>
												</span>

											</small>
											<small>
												<b>Amount:</b>
												<?php echo formatMoney($loan->loanamount) ?>
											</small>
											|
											<small>
												<b>
													Repayment:</b>
												<?php echo formatMoney($loan->collectionamount) ?>
											</small>
											|
											<small>
												<b>Rate:</b>
												<?php echo round($loan->rate, 2) . '%' ?>
											</small>
											<br>
											<small>
												<b>Created on:</b>
												<?php echo formatDate($loan->datecreated) ?>
											</small>
											<a href="<?php echo base_url('admin/customers/salary_loan_details?cid=' . $customerData->customerId) ?>" class="btn btn-primary btn-sm float-right">
												Details
											</a>


										</div>
									</li>
								<?php endforeach ?>

								<!-- /.item -->


							</ul>
						</div>
						<!-- /.card-body -->
						<div class="card-footer text-center">
							<a href="<?php echo base_url('admin/customers/loans') ?>" class="btn btn-secondary btn-sm">
								<i class="fa fa-upload"></i>
								All Loans</a>

						</div>
						<!-- /.card-footer -->
					</div>


				</div>
				<!-- /.col -->
			</div>
			<!-- /.row -->
		</div>
		<!--/. container-fluid -->
	</section>
	<!-- /.content -->
</div>
<!-- /.content-wrapper -->


<!-- PAGE PLUGINS -->
<!-- SparkLine --><script src="<?= base_url() ?>assets/plugins/sparkline/jquery.sparkline.min.js"> </script>
<!-- jVectorMap -->
<script src="<?= base_url() ?>assets/plugins/jvectormap/jquery-jvectormap-1.2.2.min.js"></script>
<script src="<?= base_url() ?>assets/plugins/jvectormap/jquery-jvectormap-world-mill-en.js"></script>
<!-- SlimScroll 1.3.0 -->
<script src="<?= base_url() ?>assets/plugins/slimScroll/jquery.slimscroll.min.js"></script>
<!-- ChartJS 1.0.2 -->
<script src="<?= base_url() ?>assets/plugins/chartjs-old/Chart.min.js"></script>

<!-- PAGE SCRIPTS -->

